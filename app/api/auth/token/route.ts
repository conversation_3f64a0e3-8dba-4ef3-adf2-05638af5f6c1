import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'

/**
 * API endpoint to get JWT token for WebSocket authentication
 * This endpoint returns the current user's JWT token that can be used
 * for WebSocket authentication
 */
export async function GET(request: NextRequest) {
  try {
    // Get the JWT token from the request
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
      secureCookie: process.env.NODE_ENV === "production"
    })

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // For security, we'll create a simplified token with only necessary fields
    // In production, you might want to create a separate short-lived token
    const webSocketToken = {
      id: token.id,
      email: token.email,
      name: token.name,
      role: token.role,
      companyId: token.companyId,
      company: token.company,
      exp: token.exp, // Keep the same expiration
      iat: token.iat, // Keep the same issued at time
    }

    // Return the token - in a real implementation, you might want to
    // sign this as a separate JWT specifically for WebSocket use
    return NextResponse.json({
      token: JSON.stringify(webSocketToken),
      user: {
        id: token.id,
        email: token.email,
        name: token.name,
        role: token.role,
        companyId: token.companyId,
        company: token.company
      }
    })

  } catch (error) {
    console.error('Token endpoint error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * Handle POST requests (not implemented)
 */
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}
