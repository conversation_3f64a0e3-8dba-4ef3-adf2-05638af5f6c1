import { NextAuthOptions } from "next-auth"
import GoogleProvider from "next-auth/providers/google"
import { PrismaAdapter } from "@next-auth/prisma-adapter"
import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient()

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    })
  ],
  session: {
    strategy: "jwt", // Use JWT instead of database session
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: "/auth/signin",
  },
  callbacks: {
    async jwt({ token, account, user, trigger }) {
      // Initial sign in - add user data to token
      if (account && user) {
        token.accessToken = account.access_token
        token.id = user.id

        // Fetch user data from database and add to token
        const dbUser = await prisma.user.findUnique({
          where: { id: user.id },
          include: { company: true }
        })

        if (dbUser) {
          token.role = dbUser.role
          token.companyId = dbUser.companyId
          token.company = dbUser.company
          token.email = dbUser.email
          token.name = dbUser.name || undefined
          token.image = dbUser.image || undefined
        }
      }

      // Refresh user data on session update
      if (trigger === "update" && token.id) {
        const dbUser = await prisma.user.findUnique({
          where: { id: token.id as string },
          include: { company: true }
        })

        if (dbUser) {
          token.role = dbUser.role
          token.companyId = dbUser.companyId
          token.company = dbUser.company
          token.email = dbUser.email
          token.name = dbUser.name || undefined
          token.image = dbUser.image || undefined
        }
      }

      return token
    },
    async session({ session, token }) {
      // Send properties to the client from JWT token
      if (token && session.user) {
        session.user.id = token.id as string
        session.user.role = token.role as string
        session.user.companyId = token.companyId as string | null
        session.user.company = token.company as any
        session.user.email = token.email as string
        session.user.name = token.name as string
        session.user.image = token.image as string
      }
      return session
    },
    async signIn({ account }) {
      // You can add custom logic here for sign-in validation
      // For example, restrict access to specific domains
      if (account?.provider === "google") {
        // Optional: Add domain restriction
        // const allowedDomains = ["yourcompany.com"]
        // if (user.email && !allowedDomains.some(domain => user.email!.endsWith(domain))) {
        //   return false
        // }
        return true
      }
      return true
    }
  },
  events: {
    async createUser() {
      // You can add custom logic here when a new user is created
      // For example, send welcome email, assign default role, etc.
    },
    async signIn() {
      // You can add custom logic here when a user signs in
      // For example, update last login time, track analytics, etc.
    }
  },
  debug: process.env.NODE_ENV === "development",
}
