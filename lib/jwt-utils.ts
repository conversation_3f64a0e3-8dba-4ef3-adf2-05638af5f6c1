import { getToken } from "next-auth/jwt"
import { NextRequest } from "next/server"
import { JWT } from "next-auth/jwt"

/**
 * JWT token validation utilities for WebSocket authentication
 */

export interface ValidatedUser {
  id: string
  email: string
  name?: string
  role?: string
  companyId?: string | null
  company?: {
    id: string
    name: string
    domain?: string | null
  } | null
}

/**
 * Extract JWT token from various sources (cookie, query parameter, header)
 */
export function extractTokenFromRequest(request: NextRequest): string | null {
  // Try to get token from cookie (next-auth default)
  const cookieToken = request.cookies.get("next-auth.session-token")?.value ||
                     request.cookies.get("__Secure-next-auth.session-token")?.value

  if (cookieToken) {
    return cookieToken
  }

  // Try to get token from query parameter
  const url = new URL(request.url)
  const queryToken = url.searchParams.get("token")
  if (queryToken) {
    return queryToken
  }

  // Try to get token from Authorization header
  const authHeader = request.headers.get("authorization")
  if (authHeader?.startsWith("Bearer ")) {
    return authHeader.substring(7)
  }

  return null
}

/**
 * Extract JWT token from WebSocket URL or headers
 */
export function extractTokenFromWebSocket(url: string, headers?: Record<string, string>): string | null {
  // Try to get token from URL query parameter
  try {
    const urlObj = new URL(url)
    const queryToken = urlObj.searchParams.get("token")
    if (queryToken) {
      return queryToken
    }
  } catch {
    // Invalid URL, continue to other methods
  }

  // Try to get token from headers
  if (headers) {
    const authHeader = headers.authorization || headers.Authorization
    if (authHeader?.startsWith("Bearer ")) {
      return authHeader.substring(7)
    }

    // Check for custom token header
    const tokenHeader = headers["x-auth-token"] || headers["X-Auth-Token"]
    if (tokenHeader) {
      return tokenHeader
    }
  }

  return null
}

/**
 * Validate JWT token and extract user data
 */
export async function validateJWTToken(token: string): Promise<ValidatedUser | null> {
  try {
    // Create a mock request object for getToken
    const mockRequest = {
      headers: new Headers(),
      cookies: new Map([["next-auth.session-token", token]])
    } as any

    // Use next-auth's getToken to validate and decode the JWT
    const decodedToken = await getToken({
      req: mockRequest,
      secret: process.env.NEXTAUTH_SECRET,
      secureCookie: process.env.NODE_ENV === "production"
    })

    if (!decodedToken) {
      return null
    }

    // Extract user data from the token
    const user: ValidatedUser = {
      id: decodedToken.id as string,
      email: decodedToken.email as string,
      name: decodedToken.name as string,
      role: decodedToken.role as string,
      companyId: decodedToken.companyId as string | null,
      company: decodedToken.company as any
    }

    return user
  } catch (error) {
    console.error("JWT validation error:", error)
    return null
  }
}

/**
 * Validate JWT token from NextRequest object
 */
export async function validateTokenFromRequest(request: NextRequest): Promise<ValidatedUser | null> {
  try {
    const decodedToken = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
      secureCookie: process.env.NODE_ENV === "production"
    })

    if (!decodedToken) {
      return null
    }

    const user: ValidatedUser = {
      id: decodedToken.id as string,
      email: decodedToken.email as string,
      name: decodedToken.name as string,
      role: decodedToken.role as string,
      companyId: decodedToken.companyId as string | null,
      company: decodedToken.company as any
    }

    return user
  } catch (error) {
    console.error("Token validation error:", error)
    return null
  }
}

/**
 * Create a JWT token for WebSocket authentication
 * This function creates a token that can be used in WebSocket connections
 */
export async function createWebSocketToken(user: ValidatedUser): Promise<string | null> {
  try {
    // For WebSocket authentication, we'll use the session token directly
    // This is a simplified approach - in production, you might want to create
    // a separate short-lived token specifically for WebSocket connections
    
    // Note: This function is primarily for future use if you need to create
    // custom tokens for WebSocket connections. For now, we'll use the existing
    // next-auth session tokens.
    
    return null // Placeholder - use existing session tokens
  } catch (error) {
    console.error("Token creation error:", error)
    return null
  }
}

/**
 * Check if a JWT token is expired
 */
export function isTokenExpired(token: JWT): boolean {
  if (!token.exp) {
    return true
  }
  
  const currentTime = Math.floor(Date.now() / 1000)
  return token.exp < currentTime
}

/**
 * Refresh user data in JWT token
 * This can be used to update user information without requiring re-authentication
 */
export async function refreshTokenUserData(token: JWT): Promise<JWT | null> {
  try {
    // This would typically involve fetching fresh user data from the database
    // and updating the token. For now, we'll return the token as-is since
    // the auth configuration already handles token refresh.
    
    return token
  } catch (error) {
    console.error("Token refresh error:", error)
    return null
  }
}
