'use client';

import { extractTokenFromWebSocket, validateJ<PERSON><PERSON><PERSON>, ValidatedUser } from './jwt-utils'

/**
 * WebSocket authentication middleware and utilities
 */

export interface WebSocketAuthResult {
  success: boolean
  user?: ValidatedUser
  error?: string
}

export interface WebSocketConnectionOptions {
  url: string
  token?: string
  headers?: Record<string, string>
}

/**
 * Authenticate WebSocket connection using JWT token
 */
export async function authenticateWebSocketConnection(
  url: string, 
  headers?: Record<string, string>
): Promise<WebSocketAuthResult> {
  try {
    // Extract token from URL or headers
    const token = extractTokenFromWebSocket(url, headers)
    
    if (!token) {
      return {
        success: false,
        error: "No authentication token provided"
      }
    }

    // Validate the JWT token
    const user = await validateJWTToken(token)
    
    if (!user) {
      return {
        success: false,
        error: "Invalid or expired authentication token"
      }
    }

    return {
      success: true,
      user
    }
  } catch (error) {
    console.error("WebSocket authentication error:", error)
    return {
      success: false,
      error: "Authentication failed"
    }
  }
}

/**
 * <PERSON>reate authenticated WebSocket URL with token
 */
export function createAuthenticatedWebSocketUrl(baseUrl: string, token: string): string {
  try {
    const url = new URL(baseUrl)
    url.searchParams.set('token', token)
    return url.toString()
  } catch (error) {
    console.error("Error creating authenticated WebSocket URL:", error)
    return baseUrl
  }
}

/**
 * Create WebSocket headers with authentication
 */
export function createAuthenticatedWebSocketHeaders(token: string): Record<string, string> {
  return {
    'Authorization': `Bearer ${token}`,
    'X-Auth-Token': token
  }
}

/**
 * WebSocket authentication middleware class
 */
export class WebSocketAuthMiddleware {
  private token: string | null = null
  private user: ValidatedUser | null = null
  private isAuthenticated: boolean = false

  constructor(token?: string) {
    if (token) {
      this.token = token
    }
  }

  /**
   * Set authentication token
   */
  setToken(token: string): void {
    this.token = token
    this.isAuthenticated = false
    this.user = null
  }

  /**
   * Get current authentication token
   */
  getToken(): string | null {
    return this.token
  }

  /**
   * Get authenticated user
   */
  getUser(): ValidatedUser | null {
    return this.user
  }

  /**
   * Check if currently authenticated
   */
  isAuth(): boolean {
    return this.isAuthenticated && this.user !== null
  }

  /**
   * Authenticate with current token
   */
  async authenticate(): Promise<WebSocketAuthResult> {
    if (!this.token) {
      return {
        success: false,
        error: "No authentication token available"
      }
    }

    try {
      const user = await validateJWTToken(this.token)
      
      if (!user) {
        this.isAuthenticated = false
        this.user = null
        return {
          success: false,
          error: "Invalid or expired authentication token"
        }
      }

      this.isAuthenticated = true
      this.user = user
      
      return {
        success: true,
        user
      }
    } catch (error) {
      console.error("Authentication error:", error)
      this.isAuthenticated = false
      this.user = null
      
      return {
        success: false,
        error: "Authentication failed"
      }
    }
  }

  /**
   * Create authenticated WebSocket connection options
   */
  createConnectionOptions(baseUrl: string): WebSocketConnectionOptions {
    if (!this.token) {
      return { url: baseUrl }
    }

    return {
      url: createAuthenticatedWebSocketUrl(baseUrl, this.token),
      token: this.token,
      headers: createAuthenticatedWebSocketHeaders(this.token)
    }
  }

  /**
   * Clear authentication
   */
  clearAuth(): void {
    this.token = null
    this.user = null
    this.isAuthenticated = false
  }
}

/**
 * Global WebSocket authentication middleware instance
 */
export const webSocketAuth = new WebSocketAuthMiddleware()

/**
 * Hook for WebSocket authentication status
 */
export function useWebSocketAuth() {
  return {
    isAuthenticated: webSocketAuth.isAuth(),
    user: webSocketAuth.getUser(),
    token: webSocketAuth.getToken(),
    setToken: (token: string) => webSocketAuth.setToken(token),
    authenticate: () => webSocketAuth.authenticate(),
    clearAuth: () => webSocketAuth.clearAuth(),
    createConnectionOptions: (baseUrl: string) => webSocketAuth.createConnectionOptions(baseUrl)
  }
}

/**
 * Validate WebSocket connection before establishing
 */
export async function validateWebSocketConnection(
  url: string,
  headers?: Record<string, string>
): Promise<{ valid: boolean; user?: ValidatedUser; error?: string }> {
  const authResult = await authenticateWebSocketConnection(url, headers)
  
  return {
    valid: authResult.success,
    user: authResult.user,
    error: authResult.error
  }
}

/**
 * Create WebSocket connection with authentication
 */
export async function createAuthenticatedWebSocket(
  baseUrl: string,
  token: string,
  onOpen?: (event: Event) => void,
  onMessage?: (event: MessageEvent) => void,
  onError?: (event: Event) => void,
  onClose?: (event: CloseEvent) => void
): Promise<WebSocket | null> {
  try {
    // Validate token first
    const user = await validateJWTToken(token)
    if (!user) {
      console.error("Invalid token for WebSocket connection")
      return null
    }

    // Create authenticated URL
    const authenticatedUrl = createAuthenticatedWebSocketUrl(baseUrl, token)
    
    // Create WebSocket connection
    const ws = new WebSocket(authenticatedUrl)
    
    // Set up event handlers
    if (onOpen) ws.onopen = onOpen
    if (onMessage) ws.onmessage = onMessage
    if (onError) ws.onerror = onError
    if (onClose) ws.onclose = onClose
    
    return ws
  } catch (error) {
    console.error("Error creating authenticated WebSocket:", error)
    return null
  }
}
