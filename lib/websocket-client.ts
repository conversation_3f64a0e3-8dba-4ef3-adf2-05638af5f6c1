'use client';

import { WebSocketMessagePayload, Message } from './schemas';
import { validateJWTToken, ValidatedUser } from './jwt-utils';
import { createAuthenticatedWebSocketUrl } from './websocket-auth';

export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

export interface WebSocketRequest {
  id: string;
  type: 'chat_message';
  payload: WebSocketMessagePayload;
  timestamp: number;
}

export interface WebSocketResponse {
  id: string;
  type: 'chat_response' | 'error';
  payload: string | { error: string };
  timestamp: number;
}

export interface ConnectionStatusListener {
  (status: ConnectionStatus): void;
}

export interface MessageListener {
  (response: WebSocketResponse): void;
}

class WebSocketClient {
  private ws: WebSocket | null = null;
  private baseUrl: string;
  private authToken: string | null = null;
  private authenticatedUser: ValidatedUser | null = null;
  private connectionStatus: ConnectionStatus = 'disconnected';
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private maxReconnectDelay = 30000; // Max 30 seconds
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;

  private statusListeners: Set<ConnectionStatusListener> = new Set();
  private messageListeners: Set<MessageListener> = new Set();
  private pendingRequests: Map<string, {
    resolve: (response: WebSocketResponse) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
  }> = new Map();

  private messageQueue: WebSocketRequest[] = [];

  constructor(url?: string) {
    this.baseUrl = url || process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'wss://gate.finityhub.ai/ws';
  }

  /**
   * Set authentication token for WebSocket connections
   */
  setAuthToken(token: string | null): void {
    this.authToken = token;
    this.authenticatedUser = null;
  }

  /**
   * Get current authentication token
   */
  getAuthToken(): string | null {
    return this.authToken;
  }

  /**
   * Get authenticated user information
   */
  getAuthenticatedUser(): ValidatedUser | null {
    return this.authenticatedUser;
  }

  /**
   * Get the WebSocket URL with authentication
   */
  private getAuthenticatedUrl(): string {
    if (this.authToken) {
      return createAuthenticatedWebSocketUrl(this.baseUrl, this.authToken);
    }
    return this.baseUrl;
  }

  connect(): Promise<void> {
    return new Promise(async (resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      this.setConnectionStatus('connecting');

      try {
        // Validate authentication token if provided
        if (this.authToken) {
          const user = await validateJWTToken(this.authToken);
          if (!user) {
            this.setConnectionStatus('error');
            reject(new Error('Invalid or expired authentication token'));
            return;
          }
          this.authenticatedUser = user;
        }

        // Get the authenticated URL
        const wsUrl = this.getAuthenticatedUrl();
        this.ws = new WebSocket(wsUrl);

        const connectTimeout = setTimeout(() => {
          this.ws?.close();
          reject(new Error('Connection timeout'));
        }, 5000);

        this.ws.onopen = () => {
          clearTimeout(connectTimeout);
          this.setConnectionStatus('connected');
          this.reconnectAttempts = 0;
          this.reconnectDelay = 1000;
          this.startHeartbeat();
          this.processMessageQueue();
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event.data);
        };

        this.ws.onclose = (event) => {
          clearTimeout(connectTimeout);
          this.cleanup();

          // Check for authentication-related close codes
          if (event.code === 1008 || event.code === 4001) {
            // Authentication failed
            this.setConnectionStatus('error');
            reject(new Error('WebSocket authentication failed'));
            return;
          }

          if (event.code !== 1000) { // Not a normal closure
            this.setConnectionStatus('error');
            this.scheduleReconnect();
          } else {
            this.setConnectionStatus('disconnected');
          }
        };

        this.ws.onerror = () => {
          clearTimeout(connectTimeout);
          this.setConnectionStatus('error');
          reject(new Error('WebSocket connection failed'));
        };

      } catch (error) {
        this.setConnectionStatus('error');
        reject(error);
      }
    });
  }

  /**
   * Connect with authentication token
   */
  connectWithAuth(token: string): Promise<void> {
    this.setAuthToken(token);
    return this.connect();
  }

  /**
   * Reconnect with current authentication
   */
  reconnectWithAuth(): Promise<void> {
    if (!this.authToken) {
      return Promise.reject(new Error('No authentication token available'));
    }
    return this.connect();
  }

  disconnect(): void {
    this.cleanup();
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    this.setConnectionStatus('disconnected');
  }

  sendMessage(messages: Message[]): Promise<WebSocketResponse> {
    return new Promise((resolve, reject) => {
      const request: WebSocketRequest = {
        id: this.generateRequestId(),
        type: 'chat_message',
        payload: { messages },
        timestamp: Date.now(),
      };

      // Set up response handling
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(request.id);
        reject(new Error('Request timeout'));
      }, 30000); // 30 second timeout

      this.pendingRequests.set(request.id, {
        resolve,
        reject,
        timeout,
      });

      if (this.connectionStatus === 'connected' && this.ws?.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(request));
      } else {
        // Queue the message for when connection is restored
        this.messageQueue.push(request);

        // Try to connect if not already connecting
        if (this.connectionStatus === 'disconnected') {
          this.connect().catch(() => {
            // Connection failed, reject the request
            this.pendingRequests.delete(request.id);
            clearTimeout(timeout);
            reject(new Error('Failed to establish WebSocket connection'));
          });
        }
      }
    });
  }

  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  onStatusChange(listener: ConnectionStatusListener): () => void {
    this.statusListeners.add(listener);
    return () => this.statusListeners.delete(listener);
  }

  onMessage(listener: MessageListener): () => void {
    this.messageListeners.add(listener);
    return () => this.messageListeners.delete(listener);
  }

  private setConnectionStatus(status: ConnectionStatus): void {
    if (this.connectionStatus !== status) {
      this.connectionStatus = status;
      this.statusListeners.forEach(listener => listener(status));
    }
  }

  private handleMessage(data: string): void {
    try {
      const response: WebSocketResponse = JSON.parse(data);

      // Handle pending request
      const pendingRequest = this.pendingRequests.get(response.id);
      if (pendingRequest) {
        clearTimeout(pendingRequest.timeout);
        this.pendingRequests.delete(response.id);
        pendingRequest.resolve(response);
      }

      // Notify message listeners
      this.messageListeners.forEach(listener => listener(response));

    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        // Send a ping frame (browser WebSocket doesn't have ping method, so we send a custom message)
        try {
          this.ws.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
        } catch (error) {
          console.warn('Failed to send heartbeat:', error);
        }
      }
    }, 30000); // 30 seconds
  }

  private cleanup(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    // Reject all pending requests
    this.pendingRequests.forEach(({ reject, timeout }) => {
      clearTimeout(timeout);
      reject(new Error('Connection closed'));
    });
    this.pendingRequests.clear();
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);

    this.reconnectTimeout = setTimeout(() => {
      this.connect().catch(() => {
        // Reconnection failed, will try again
      });
    }, delay);
  }

  private processMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.ws?.readyState === WebSocket.OPEN) {
      const request = this.messageQueue.shift();
      if (request) {
        this.ws.send(JSON.stringify(request));
      }
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}

// Export singleton instance
export const webSocketClient = new WebSocketClient();
